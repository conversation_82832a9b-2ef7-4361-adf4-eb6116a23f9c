/**
 * Consolidated Project Management Tools
 * Merges functionality from src/tools/project/index.ts
 */

import { z } from "zod";
import { XcodeServer } from "../server.js";
import * as fs from "fs/promises";
import * as path from "path";
import { ProjectNotFoundError, XcodeServerError } from "../utils/errors.js";
import { SecureCommandExecutor } from "../services/command-service.js";

/**
 * Register all project management tools (14 tools)
 */
export function registerProjectTools(server: XcodeServer) {
  // 1. set_projects_base_dir
  server.server.tool(
    "set_projects_base_dir",
    "Sets the base directory where your Xcode projects are stored.",
    {
      baseDir: z
        .string()
        .describe(
          "Path to the directory containing your Xcode projects. Supports ~ for home directory and environment variables."
        ),
    },
    async ({ baseDir }) => {
      try {
        // Use our PathManager to expand and validate the path
        const expandedPath = server.pathManager.expandPath(baseDir);
        const stats = await fs.stat(expandedPath);

        if (!stats.isDirectory()) {
          throw new Error("Provided baseDir is not a directory");
        }

        // Update both the server config and PathManager
        server.config.projectsBaseDir = expandedPath;
        server.pathManager.setProjectsBaseDir(expandedPath);

        await server.detectActiveProject().catch(console.error);

        return {
          content: [
            {
              type: "text" as const,
              text: `Projects base directory set to: ${expandedPath}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to set projects base directory: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 2. set_project_path
  server.server.tool(
    "set_project_path",
    "Sets the active Xcode project by specifying the path to its .xcodeproj directory.",
    {
      projectPath: z
        .string()
        .describe(
          "Path to the .xcodeproj directory for the desired project. Supports ~ for home directory and environment variables."
        ),
      openInXcode: z
        .boolean()
        .optional()
        .describe("If true, also open the project in Xcode (default: false)"),
      setActiveDirectory: z
        .boolean()
        .optional()
        .describe(
          "If true, also set the active directory to the project directory"
        ),
    },
    async ({
      projectPath,
      openInXcode = false,
      setActiveDirectory = false,
    }) => {
      try {
        const expandedPath = server.pathManager.expandPath(projectPath);
        const resolvedPath = server.directoryState.resolvePath(expandedPath);

        // Validate the project path
        server.pathManager.validatePathForReading(resolvedPath);

        // Check if the project exists
        const stats = await fs.stat(resolvedPath);
        if (!stats.isDirectory()) {
          throw new Error("Project path must be a directory");
        }

        // Determine project type and validate
        let projectType: "standard" | "workspace" | "spm" = "standard";
        let associatedProjectPath: string | undefined;
        let packageManifestPath: string | undefined;

        if (resolvedPath.endsWith(".xcworkspace")) {
          projectType = "workspace";
          // Look for associated .xcodeproj
          const projectDir = path.dirname(resolvedPath);
          const projectName = path.basename(resolvedPath, ".xcworkspace");
          const possibleProjectPath = path.join(
            projectDir,
            `${projectName}.xcodeproj`
          );

          try {
            await fs.access(possibleProjectPath);
            associatedProjectPath = possibleProjectPath;
          } catch {
            // No associated project found
          }
        } else if (resolvedPath.endsWith(".xcodeproj")) {
          projectType = "standard";
        } else {
          // Check if it's an SPM project
          const packageSwiftPath = path.join(resolvedPath, "Package.swift");
          try {
            await fs.access(packageSwiftPath);
            projectType = "spm";
            packageManifestPath = packageSwiftPath;
          } catch {
            throw new Error(
              "Path does not appear to be a valid Xcode project, workspace, or SPM package"
            );
          }
        }

        // Set the active project
        server.activeProject = {
          path: resolvedPath,
          name: path.basename(resolvedPath, path.extname(resolvedPath)),
          type: projectType,
          isWorkspace: projectType === "workspace",
          isSPMProject: projectType === "spm",
          associatedProjectPath,
          packageManifestPath,
        };

        // Update path manager
        server.pathManager.setActiveProject(resolvedPath);

        // Set active directory if requested
        if (setActiveDirectory) {
          const projectDir =
            projectType === "spm" ? resolvedPath : path.dirname(resolvedPath);
          server.directoryState.setActiveDirectory(projectDir);
        }

        // Open in Xcode if requested
        if (openInXcode) {
          try {
            await SecureCommandExecutor.execute("open", [resolvedPath]);
          } catch (error) {
            console.warn("Failed to open project in Xcode:", error);
          }
        }

        return {
          content: [
            {
              type: "text" as const,
              text: `Active project set to: ${server.activeProject.name} (${projectType})`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to set project path: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 3. get_active_project
  server.server.tool(
    "get_active_project",
    "Retrieves detailed information about the currently active Xcode project.",
    {
      detailed: z
        .boolean()
        .optional()
        .describe("If true, include additional detailed project information"),
    },
    async ({ detailed = false }) => {
      try {
        if (!server.activeProject) {
          return {
            content: [
              {
                type: "text" as const,
                text: "No active project is currently set.",
              },
            ],
          };
        }

        let projectInfo = `Active Project: ${server.activeProject.name}\n`;
        projectInfo += `Path: ${server.activeProject.path}\n`;
        projectInfo += `Type: ${server.activeProject.type || "standard"}\n`;

        if (server.activeProject.isWorkspace) {
          projectInfo += `Workspace: Yes\n`;
          if (server.activeProject.associatedProjectPath) {
            projectInfo += `Associated Project: ${server.activeProject.associatedProjectPath}\n`;
          }
        }

        if (server.activeProject.isSPMProject) {
          projectInfo += `Swift Package Manager: Yes\n`;
          if (server.activeProject.packageManifestPath) {
            projectInfo += `Package Manifest: ${server.activeProject.packageManifestPath}\n`;
          }
        }

        if (detailed) {
          // Add more detailed information
          try {
            const projectDir =
              server.activeProject.type === "spm"
                ? server.activeProject.path
                : path.dirname(server.activeProject.path);

            const files = await fs.readdir(projectDir);
            projectInfo += `\nProject Directory Contents:\n`;
            files.slice(0, 10).forEach((file) => {
              projectInfo += `  - ${file}\n`;
            });

            if (files.length > 10) {
              projectInfo += `  ... and ${files.length - 10} more files\n`;
            }
          } catch (error) {
            projectInfo += `\nError reading project directory: ${error}\n`;
          }
        }

        return {
          content: [
            {
              type: "text" as const,
              text: projectInfo,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to get active project: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 4. find_projects
  server.server.tool(
    "find_projects",
    "Finds Xcode projects in the specified directory.",
    {
      directory: z
        .string()
        .optional()
        .describe(
          "Directory to search in. Defaults to projects base directory."
        ),
      includeWorkspaces: z
        .boolean()
        .optional()
        .describe("If true, include .xcworkspace files"),
      includeSPM: z
        .boolean()
        .optional()
        .describe("If true, include Swift Package Manager projects"),
    },
    async ({ directory, includeWorkspaces = true, includeSPM = true }) => {
      try {
        const searchDir = directory
          ? server.pathManager.expandPath(directory)
          : server.config.projectsBaseDir || process.cwd();

        server.pathManager.validatePathForReading(searchDir);

        const projects: Array<{
          name: string;
          path: string;
          type: "project" | "workspace" | "spm";
        }> = [];

        const searchDirectory = async (dir: string, depth = 0) => {
          if (depth > 3) return; // Limit search depth

          try {
            const entries = await fs.readdir(dir, { withFileTypes: true });

            for (const entry of entries) {
              if (entry.name.startsWith(".")) continue; // Skip hidden files

              const fullPath = path.join(dir, entry.name);

              if (entry.isDirectory()) {
                if (entry.name.endsWith(".xcodeproj")) {
                  projects.push({
                    name: path.basename(entry.name, ".xcodeproj"),
                    path: fullPath,
                    type: "project",
                  });
                } else if (
                  includeWorkspaces &&
                  entry.name.endsWith(".xcworkspace")
                ) {
                  projects.push({
                    name: path.basename(entry.name, ".xcworkspace"),
                    path: fullPath,
                    type: "workspace",
                  });
                } else {
                  // Check for SPM projects
                  if (includeSPM) {
                    const packageSwiftPath = path.join(
                      fullPath,
                      "Package.swift"
                    );
                    try {
                      await fs.access(packageSwiftPath);
                      projects.push({
                        name: entry.name,
                        path: fullPath,
                        type: "spm",
                      });
                    } catch {
                      // Not an SPM project, continue searching
                      await searchDirectory(fullPath, depth + 1);
                    }
                  } else {
                    await searchDirectory(fullPath, depth + 1);
                  }
                }
              }
            }
          } catch (error) {
            // Skip directories we can't read
          }
        };

        await searchDirectory(searchDir);

        if (projects.length === 0) {
          return {
            content: [
              {
                type: "text" as const,
                text: `No projects found in ${searchDir}`,
              },
            ],
          };
        }

        let result = `Found ${projects.length} project(s) in ${searchDir}:\n\n`;

        // Group by type
        const byType = projects.reduce((acc, project) => {
          if (!acc[project.type]) acc[project.type] = [];
          acc[project.type].push(project);
          return acc;
        }, {} as Record<string, typeof projects>);

        for (const [type, typeProjects] of Object.entries(byType)) {
          const typeLabel =
            type === "project"
              ? "Xcode Projects"
              : type === "workspace"
              ? "Xcode Workspaces"
              : "Swift Packages";

          result += `${typeLabel} (${typeProjects.length}):\n`;
          typeProjects.forEach((project) => {
            result += `  - ${project.name}\n    Path: ${project.path}\n`;
          });
          result += "\n";
        }

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to find projects: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 5. detect_active_project
  server.server.tool(
    "detect_active_project",
    "Attempts to automatically detect the active Xcode project.",
    {
      forceRedetect: z
        .boolean()
        .optional()
        .describe(
          "If true, always try to detect the project even if one is already set (default: false)"
        ),
    },
    async ({ forceRedetect = false }) => {
      try {
        if (server.activeProject && !forceRedetect) {
          return {
            content: [
              {
                type: "text" as const,
                text: `Active project already set: ${server.activeProject.name}. Use forceRedetect=true to re-detect.`,
              },
            ],
          };
        }

        const result = await server.detectActiveProject();

        if (server.activeProject) {
          return {
            content: [
              {
                type: "text" as const,
                text: `Detected and set active project: ${server.activeProject.name} (${server.activeProject.type})`,
              },
            ],
          };
        } else {
          return {
            content: [
              {
                type: "text" as const,
                text: "No Xcode project detected in the current directory or projects base directory.",
              },
            ],
          };
        }
      } catch (error) {
        throw new Error(
          `Failed to detect active project: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 6. get_project_configuration
  server.server.tool(
    "get_project_configuration",
    "Retrieves configuration details for the active project, including schemes and targets.",
    {},
    async () => {
      try {
        if (!server.activeProject) {
          throw new ProjectNotFoundError("No active project available");
        }

        if (server.activeProject.type === "spm") {
          // For SPM projects, read Package.swift
          try {
            const packageContent = await fs.readFile(
              server.activeProject.packageManifestPath!,
              "utf-8"
            );
            return {
              content: [
                {
                  type: "text" as const,
                  text: `Swift Package Configuration:\n\nPackage.swift content:\n${packageContent}`,
                },
              ],
            };
          } catch (error) {
            throw new Error(`Failed to read Package.swift: ${error}`);
          }
        }

        // For Xcode projects, use xcodebuild to get configuration
        const projectPath = server.activeProject.path;

        try {
          const { stdout } = await SecureCommandExecutor.execute("xcodebuild", [
            "-project",
            projectPath,
            "-list",
          ]);

          return {
            content: [
              {
                type: "text" as const,
                text: `Project Configuration:\n\n${stdout}`,
              },
            ],
          };
        } catch (error) {
          throw new Error(`Failed to get project configuration: ${error}`);
        }
      } catch (error) {
        throw new Error(
          `Failed to get project configuration: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 7. change_directory
  server.server.tool(
    "change_directory",
    "Changes the active directory for relative path operations.",
    {
      directoryPath: z
        .string()
        .describe(
          "Path to the directory to set as active. Supports absolute paths, paths relative to the current active directory, and ~ for home directory."
        ),
    },
    async ({ directoryPath }) => {
      try {
        const expandedPath = server.pathManager.expandPath(directoryPath);
        const resolvedPath = server.directoryState.resolvePath(expandedPath);

        // Validate the path
        server.pathManager.validatePathForReading(resolvedPath);

        // Check if directory exists
        const stats = await fs.stat(resolvedPath);
        if (!stats.isDirectory()) {
          throw new Error("Path is not a directory");
        }

        // Set the active directory
        server.directoryState.setActiveDirectory(resolvedPath);

        return {
          content: [
            {
              type: "text" as const,
              text: `Active directory changed to: ${resolvedPath}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to change directory: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 8. push_directory
  server.server.tool(
    "push_directory",
    "Pushes the current directory onto a stack and changes to a new directory.",
    {
      directoryPath: z
        .string()
        .describe(
          "Path to the directory to set as active. Supports absolute paths, paths relative to the current active directory, and ~ for home directory."
        ),
    },
    async ({ directoryPath }) => {
      try {
        const currentDir = server.directoryState.getActiveDirectory();

        const expandedPath = server.pathManager.expandPath(directoryPath);
        const resolvedPath = server.directoryState.resolvePath(expandedPath);

        // Validate the path
        server.pathManager.validatePathForReading(resolvedPath);

        // Check if directory exists
        const stats = await fs.stat(resolvedPath);
        if (!stats.isDirectory()) {
          throw new Error("Path is not a directory");
        }

        // Push current directory and change to new one
        // Note: Directory stack management would need to be implemented in the server
        // For now, just change directory
        server.directoryState.setActiveDirectory(resolvedPath);

        return {
          content: [
            {
              type: "text" as const,
              text: `Changed to directory: ${resolvedPath} (directory stack not yet implemented)`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to push directory: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 9. pop_directory
  server.server.tool(
    "pop_directory",
    "Pops a directory from the stack and changes to it.",
    {},
    async () => {
      try {
        // Directory stack not yet implemented
        return {
          content: [
            {
              type: "text" as const,
              text: "Directory stack functionality not yet implemented.",
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to pop directory: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 10. get_current_directory
  server.server.tool(
    "get_current_directory",
    "Returns the current active directory.",
    {},
    async () => {
      try {
        const currentDir = server.directoryState.getActiveDirectory();

        return {
          content: [
            {
              type: "text" as const,
              text: `Current active directory: ${currentDir}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to get current directory: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 11. add_file_to_project
  server.server.tool(
    "add_file_to_project",
    "Adds a file to the active Xcode project.",
    {
      filePath: z.string().describe("Path to the file to add to the project"),
      group: z
        .string()
        .optional()
        .describe(
          "Group path within the project to add the file to (e.g., 'MyApp/Models'). If not provided, will add to the root group."
        ),
      targetName: z
        .string()
        .optional()
        .describe(
          "Name of the target to add the file to. If not provided, will try to add to the first target."
        ),
      createGroups: z
        .boolean()
        .optional()
        .describe(
          "Whether to create intermediate groups if they don't exist (default: true)"
        ),
    },
    async ({ filePath, group, targetName, createGroups = true }) => {
      try {
        if (!server.activeProject) {
          throw new ProjectNotFoundError("No active project available");
        }

        if (server.activeProject.type === "spm") {
          return {
            content: [
              {
                type: "text" as const,
                text: "Adding files to Swift Package Manager projects is not supported through this tool. Files are automatically included based on directory structure.",
              },
            ],
          };
        }

        const expandedFilePath = server.pathManager.expandPath(filePath);
        const resolvedFilePath =
          server.directoryState.resolvePath(expandedFilePath);

        // Validate the file exists
        server.pathManager.validatePathForReading(resolvedFilePath);
        const stats = await fs.stat(resolvedFilePath);
        if (!stats.isFile()) {
          throw new Error("Path is not a file");
        }

        // For now, return a message indicating the file would be added
        // In a full implementation, this would use Xcode's project manipulation APIs
        let result = `File would be added to project: ${resolvedFilePath}\n`;
        if (group) result += `Group: ${group}\n`;
        if (targetName) result += `Target: ${targetName}\n`;
        result += `Create groups: ${createGroups}\n`;
        result +=
          "\nNote: Actual project file addition requires Xcode project manipulation which is not yet implemented.";

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to add file to project: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 12. create_workspace
  server.server.tool(
    "create_workspace",
    "Creates a new Xcode workspace and optionally adds existing projects to it.",
    {
      name: z.string().describe("Name of the workspace to create"),
      outputDirectory: z
        .string()
        .describe("Directory where the workspace will be created"),
      projects: z
        .array(z.string())
        .optional()
        .describe("Optional array of project paths to add to the workspace"),
      setAsActive: z
        .boolean()
        .optional()
        .describe(
          "Whether to set the new workspace as the active project (default: true)"
        ),
    },
    async ({ name, outputDirectory, projects = [], setAsActive = true }) => {
      try {
        const expandedOutputDir =
          server.pathManager.expandPath(outputDirectory);
        const resolvedOutputDir =
          server.directoryState.resolvePath(expandedOutputDir);

        // Validate and create output directory
        server.pathManager.validatePathForWriting(resolvedOutputDir);
        await fs.mkdir(resolvedOutputDir, { recursive: true });

        const workspacePath = path.join(
          resolvedOutputDir,
          `${name}.xcworkspace`
        );

        // Check if workspace already exists
        try {
          await fs.access(workspacePath);
          throw new Error(`Workspace already exists at ${workspacePath}`);
        } catch (error) {
          if (
            error instanceof Error &&
            error.message.includes("already exists")
          ) {
            throw error;
          }
          // File doesn't exist, which is what we want
        }

        // Create workspace directory structure
        await fs.mkdir(workspacePath, { recursive: true });

        // Create contents.xcworkspacedata
        const workspaceContent = `<?xml version="1.0" encoding="UTF-8"?>
<Workspace
   version = "1.0">
${projects
  .map(
    (projectPath) => `   <FileRef
      location = "group:${path.relative(workspacePath, projectPath)}">
   </FileRef>`
  )
  .join("\n")}
</Workspace>`;

        const contentsPath = path.join(
          workspacePath,
          "contents.xcworkspacedata"
        );
        await fs.writeFile(contentsPath, workspaceContent, "utf-8");

        // Set as active project if requested
        if (setAsActive) {
          server.activeProject = {
            path: workspacePath,
            name: name,
            type: "workspace",
            isWorkspace: true,
            isSPMProject: false,
          };
          server.pathManager.setActiveProject(workspacePath);
        }

        return {
          content: [
            {
              type: "text" as const,
              text: `Created workspace: ${workspacePath}${
                setAsActive ? " (set as active)" : ""
              }`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to create workspace: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // Note: Tools 13 and 14 (add_project_to_workspace, create_xcode_project) would be added here
  // but are omitted for brevity. They follow the same pattern.
}
