/**
 * Unified performance monitoring and regression detection system
 * Combines functionality from performance.ts and performanceRegression.ts
 */

import { CacheManager } from "../services/cache-service.js";

/**
 * Performance metric data
 */
export interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

/**
 * Performance statistics
 */
export interface PerformanceStats {
  totalCalls: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  totalDuration: number;
  recentMetrics: PerformanceMetric[];
}

/**
 * Performance baseline for regression detection
 */
export interface PerformanceBaseline {
  operationName: string;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  standardDeviation: number;
  sampleSize: number;
  timestamp: Date;
  version: string;
}

/**
 * Performance regression alert
 */
export interface PerformanceRegression {
  operationName: string;
  currentDuration: number;
  baselineDuration: number;
  regressionPercentage: number;
  severity: "low" | "medium" | "high" | "critical";
  timestamp: Date;
  context?: Record<string, unknown>;
}

/**
 * Performance trend analysis
 */
export interface PerformanceTrend {
  operationName: string;
  trend: "improving" | "stable" | "degrading";
  changePercentage: number;
  dataPoints: number;
  timespan: number;
}

/**
 * Performance monitoring configuration
 */
export interface PerformanceMonitoringConfig {
  enabled: boolean;
  maxMetricsPerOperation: number;
  regressionThresholds: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  baselineUpdateInterval: number;
  alertCallbacks: Array<(regression: PerformanceRegression) => void>;
  trendAnalysisWindow: number;
}

/**
 * Unified performance monitor with regression detection
 */
export class UnifiedPerformanceMonitor {
  private metrics = new Map<string, PerformanceMetric[]>();
  private baselines = new Map<string, PerformanceBaseline>();
  private regressions: PerformanceRegression[] = [];
  private trends = new Map<string, PerformanceTrend>();
  private config: PerformanceMonitoringConfig;
  private cache = CacheManager.getCache<PerformanceBaseline>(
    "performance-baselines"
  );

  constructor(config: Partial<PerformanceMonitoringConfig> = {}) {
    this.config = {
      enabled: true,
      maxMetricsPerOperation: 100,
      regressionThresholds: {
        low: 0.1, // 10%
        medium: 0.25, // 25%
        high: 0.5, // 50%
        critical: 1.0, // 100%
      },
      baselineUpdateInterval: 3600000, // 1 hour
      alertCallbacks: [],
      trendAnalysisWindow: 86400000, // 24 hours
      ...config,
    };

    if (this.config.enabled) {
      this.startMonitoring();
    }
  }

  /**
   * Start timing an operation
   */
  startTimer(operationName: string): PerformanceTimer {
    return new PerformanceTimer(operationName, this);
  }

  /**
   * Record a performance metric
   */
  recordMetric(metric: PerformanceMetric): void {
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }

    const operationMetrics = this.metrics.get(metric.name)!;
    operationMetrics.push(metric);

    // Keep only the most recent metrics
    if (operationMetrics.length > this.config.maxMetricsPerOperation) {
      operationMetrics.shift();
    }
  }

  /**
   * Get statistics for an operation
   */
  getStats(operationName: string): PerformanceStats | null {
    const metrics = this.metrics.get(operationName);
    if (!metrics || metrics.length === 0) {
      return null;
    }

    const durations = metrics.map((m) => m.duration);
    const totalDuration = durations.reduce((sum, d) => sum + d, 0);

    return {
      totalCalls: metrics.length,
      averageDuration: totalDuration / metrics.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      totalDuration,
      recentMetrics: [...metrics].slice(-10), // Last 10 metrics
    };
  }

  /**
   * Get all operation names being monitored
   */
  getOperationNames(): string[] {
    return Array.from(this.metrics.keys());
  }

  /**
   * Get a summary of all operations
   */
  getAllStats(): Record<string, PerformanceStats> {
    const stats: Record<string, PerformanceStats> = {};

    for (const operationName of this.getOperationNames()) {
      const operationStats = this.getStats(operationName);
      if (operationStats) {
        stats[operationName] = operationStats;
      }
    }

    return stats;
  }

  /**
   * Get slow operations (above threshold)
   */
  getSlowOperations(
    thresholdMs = 1000
  ): Array<{ name: string; stats: PerformanceStats }> {
    const slowOps: Array<{ name: string; stats: PerformanceStats }> = [];

    for (const operationName of this.getOperationNames()) {
      const stats = this.getStats(operationName);
      if (stats && stats.averageDuration > thresholdMs) {
        slowOps.push({ name: operationName, stats });
      }
    }

    return slowOps.sort(
      (a, b) => b.stats.averageDuration - a.stats.averageDuration
    );
  }

  /**
   * Record a performance baseline
   */
  async recordBaseline(
    operationName: string,
    version: string = "1.0.0"
  ): Promise<void> {
    const stats = this.getStats(operationName);

    if (!stats || stats.totalCalls < 10) {
      // Need at least 10 samples for a reliable baseline
      return;
    }

    const baseline: PerformanceBaseline = {
      operationName,
      averageDuration: stats.averageDuration,
      minDuration: stats.minDuration,
      maxDuration: stats.maxDuration,
      standardDeviation: this.calculateStandardDeviation(stats.recentMetrics),
      sampleSize: stats.totalCalls,
      timestamp: new Date(),
      version,
    };

    this.baselines.set(operationName, baseline);

    // Cache the baseline for persistence
    await this.cache.set(operationName, baseline, {
      ttl: this.config.baselineUpdateInterval * 2,
      tags: [
        "baseline",
        version,
        `version:${version}`,
        `operation:${operationName}`,
      ],
    });
  }

  /**
   * Check for performance regressions
   */
  async checkForRegressions(): Promise<PerformanceRegression[]> {
    const regressions: PerformanceRegression[] = [];

    for (const [operationName, baseline] of this.baselines.entries()) {
      const currentStats = this.getStats(operationName);

      if (!currentStats || currentStats.totalCalls < 5) {
        continue;
      }

      const regression = this.detectRegression(
        baseline,
        currentStats.averageDuration
      );

      if (regression) {
        regressions.push(regression);
        this.regressions.push(regression);

        // Trigger alert callbacks
        for (const callback of this.config.alertCallbacks) {
          try {
            callback(regression);
          } catch (error) {
            console.error("Error in performance regression callback:", error);
          }
        }
      }
    }

    return regressions;
  }

  /**
   * Analyze performance trends
   */
  async analyzeTrends(): Promise<PerformanceTrend[]> {
    const trends: PerformanceTrend[] = [];
    const windowStart = Date.now() - this.config.trendAnalysisWindow;

    for (const operationName of this.getOperationNames()) {
      const stats = this.getStats(operationName);

      if (!stats) continue;

      const recentMetrics = stats.recentMetrics.filter(
        (metric) => metric.timestamp.getTime() > windowStart
      );

      if (recentMetrics.length < 5) continue;

      const trend = this.calculateTrend(recentMetrics);

      if (trend) {
        const trendData = {
          operationName,
          ...trend,
          dataPoints: recentMetrics.length,
          timespan: this.config.trendAnalysisWindow,
        };

        trends.push(trendData);
        this.trends.set(operationName, trendData);
      }
    }

    return trends;
  }

  /**
   * Clear metrics for an operation
   */
  clearMetrics(operationName: string): void {
    this.metrics.delete(operationName);
  }

  /**
   * Clear all metrics
   */
  clearAllMetrics(): void {
    this.metrics.clear();
    this.baselines.clear();
    this.regressions = [];
    this.trends.clear();
  }

  /**
   * Get performance regression history
   */
  getRegressionHistory(
    operationName?: string,
    since?: Date
  ): PerformanceRegression[] {
    let regressions = this.regressions;

    if (operationName) {
      regressions = regressions.filter(
        (r) => r.operationName === operationName
      );
    }

    if (since) {
      regressions = regressions.filter((r) => r.timestamp >= since);
    }

    return regressions.sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    );
  }

  /**
   * Get current performance trends
   */
  getCurrentTrends(): PerformanceTrend[] {
    return Array.from(this.trends.values());
  }

  /**
   * Add alert callback
   */
  addAlertCallback(
    callback: (regression: PerformanceRegression) => void
  ): void {
    this.config.alertCallbacks.push(callback);
  }

  /**
   * Generate comprehensive performance report
   */
  generateReport(): {
    summary: {
      totalOperations: number;
      operationsWithBaselines: number;
      recentRegressions: number;
      criticalRegressions: number;
    };
    regressions: PerformanceRegression[];
    trends: PerformanceTrend[];
    slowOperations: Array<{ name: string; stats: PerformanceStats }>;
    recommendations: string[];
  } {
    const recentRegressions = this.getRegressionHistory(
      undefined,
      new Date(Date.now() - 86400000) // Last 24 hours
    );

    const criticalRegressions = recentRegressions.filter(
      (r) => r.severity === "critical" || r.severity === "high"
    );

    const slowOperations = this.getSlowOperations(1000);
    const recommendations = this.generateRecommendations(recentRegressions);

    return {
      summary: {
        totalOperations: this.getOperationNames().length,
        operationsWithBaselines: this.baselines.size,
        recentRegressions: recentRegressions.length,
        criticalRegressions: criticalRegressions.length,
      },
      regressions: recentRegressions.slice(0, 10), // Last 10 regressions
      trends: this.getCurrentTrends(),
      slowOperations: slowOperations.slice(0, 10), // Top 10 slow operations
      recommendations,
    };
  }

  /**
   * Generate a formatted performance report
   */
  generateFormattedReport(): string {
    const allStats = this.getAllStats();
    const operationNames = Object.keys(allStats).sort();

    if (operationNames.length === 0) {
      return "No performance metrics available.";
    }

    let report = "Performance Report\n";
    report += "==================\n\n";

    for (const operationName of operationNames) {
      const stats = allStats[operationName];
      report += `Operation: ${operationName}\n`;
      report += `  Total Calls: ${stats.totalCalls}\n`;
      report += `  Average Duration: ${stats.averageDuration.toFixed(2)}ms\n`;
      report += `  Min Duration: ${stats.minDuration.toFixed(2)}ms\n`;
      report += `  Max Duration: ${stats.maxDuration.toFixed(2)}ms\n`;
      report += `  Total Duration: ${stats.totalDuration.toFixed(2)}ms\n\n`;
    }

    // Add slow operations section
    const slowOps = this.getSlowOperations(1000);
    if (slowOps.length > 0) {
      report += "Slow Operations (>1000ms average):\n";
      report += "===================================\n";
      for (const { name, stats } of slowOps) {
        report += `  ${name}: ${stats.averageDuration.toFixed(2)}ms average\n`;
      }
      report += "\n";
    }

    // Add regression information
    const recentRegressions = this.getRegressionHistory(
      undefined,
      new Date(Date.now() - 86400000)
    );
    if (recentRegressions.length > 0) {
      report += "Recent Performance Regressions:\n";
      report += "===============================\n";
      for (const regression of recentRegressions.slice(0, 5)) {
        report += `  ${
          regression.operationName
        }: ${regression.regressionPercentage.toFixed(1)}% slower (${
          regression.severity
        })\n`;
      }
      report += "\n";
    }

    return report;
  }

  // Private helper methods

  private startMonitoring(): void {
    // Update baselines periodically
    setInterval(() => {
      this.updateBaselines();
    }, this.config.baselineUpdateInterval);

    // Check for regressions periodically
    setInterval(() => {
      this.checkForRegressions();
    }, 60000); // Every minute

    // Analyze trends periodically
    setInterval(() => {
      this.analyzeTrends();
    }, 300000); // Every 5 minutes
  }

  private async updateBaselines(): Promise<void> {
    const operationNames = this.getOperationNames();

    for (const operationName of operationNames) {
      await this.recordBaseline(operationName);
    }
  }

  private detectRegression(
    baseline: PerformanceBaseline,
    currentDuration: number
  ): PerformanceRegression | null {
    const regressionRatio =
      (currentDuration - baseline.averageDuration) / baseline.averageDuration;

    if (regressionRatio <= this.config.regressionThresholds.low) {
      return null; // No significant regression
    }

    let severity: PerformanceRegression["severity"] = "low";

    if (regressionRatio >= this.config.regressionThresholds.critical) {
      severity = "critical";
    } else if (regressionRatio >= this.config.regressionThresholds.high) {
      severity = "high";
    } else if (regressionRatio >= this.config.regressionThresholds.medium) {
      severity = "medium";
    }

    return {
      operationName: baseline.operationName,
      currentDuration,
      baselineDuration: baseline.averageDuration,
      regressionPercentage: regressionRatio * 100,
      severity,
      timestamp: new Date(),
      context: {
        baselineVersion: baseline.version,
        baselineSampleSize: baseline.sampleSize,
        baselineTimestamp: baseline.timestamp,
      },
    };
  }

  private calculateTrend(
    metrics: Array<{ duration: number; timestamp: Date }>
  ): {
    trend: PerformanceTrend["trend"];
    changePercentage: number;
  } | null {
    if (metrics.length < 5) return null;

    // Simple linear regression to detect trend
    const sortedMetrics = metrics.sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
    );
    const firstHalf = sortedMetrics.slice(
      0,
      Math.floor(sortedMetrics.length / 2)
    );
    const secondHalf = sortedMetrics.slice(
      Math.floor(sortedMetrics.length / 2)
    );

    const firstAvg =
      firstHalf.reduce((sum, m) => sum + m.duration, 0) / firstHalf.length;
    const secondAvg =
      secondHalf.reduce((sum, m) => sum + m.duration, 0) / secondHalf.length;

    const changePercentage = ((secondAvg - firstAvg) / firstAvg) * 100;

    let trend: PerformanceTrend["trend"] = "stable";

    if (Math.abs(changePercentage) > 5) {
      trend = changePercentage > 0 ? "degrading" : "improving";
    }

    return { trend, changePercentage };
  }

  private calculateStandardDeviation(
    metrics: Array<{ duration: number }>
  ): number {
    if (metrics.length === 0) return 0;

    const mean =
      metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
    const squaredDiffs = metrics.map((m) => Math.pow(m.duration - mean, 2));
    const avgSquaredDiff =
      squaredDiffs.reduce((sum, diff) => sum + diff, 0) / metrics.length;

    return Math.sqrt(avgSquaredDiff);
  }

  private generateRecommendations(
    regressions: PerformanceRegression[]
  ): string[] {
    const recommendations: string[] = [];

    const criticalCount = regressions.filter(
      (r) => r.severity === "critical"
    ).length;
    const highCount = regressions.filter((r) => r.severity === "high").length;

    if (criticalCount > 0) {
      recommendations.push(
        `Critical performance regressions detected in ${criticalCount} operations. Immediate investigation required.`
      );
    }

    if (highCount > 0) {
      recommendations.push(
        `High-impact performance regressions detected in ${highCount} operations. Review and optimization recommended.`
      );
    }

    const degradingTrends = this.getCurrentTrends().filter(
      (t) => t.trend === "degrading"
    );
    if (degradingTrends.length > 0) {
      recommendations.push(
        `${degradingTrends.length} operations showing degrading performance trends. Monitor closely.`
      );
    }

    const slowOps = this.getSlowOperations(5000); // Very slow operations
    if (slowOps.length > 0) {
      recommendations.push(
        `${slowOps.length} operations are very slow (>5s average). Consider optimization.`
      );
    }

    if (recommendations.length === 0) {
      recommendations.push("Performance is stable. Continue monitoring.");
    }

    return recommendations;
  }
}

/**
 * Timer for measuring operation performance
 */
export class PerformanceTimer {
  private startTime: number;
  private endTime?: number;
  public metadata?: Record<string, unknown>;

  constructor(
    private operationName: string,
    private monitor: UnifiedPerformanceMonitor,
    metadata?: Record<string, unknown>
  ) {
    this.startTime = performance.now();
    this.metadata = metadata;
  }

  /**
   * Stop the timer and record the metric
   */
  stop(additionalMetadata?: Record<string, unknown>): number {
    this.endTime = performance.now();
    const duration = this.endTime - this.startTime;

    const metric: PerformanceMetric = {
      name: this.operationName,
      duration,
      timestamp: new Date(),
      metadata: { ...this.metadata, ...additionalMetadata },
    };

    this.monitor.recordMetric(metric);
    return duration;
  }

  /**
   * Get the current elapsed time without stopping the timer
   */
  getElapsed(): number {
    return performance.now() - this.startTime;
  }
}

/**
 * Global performance monitor instance
 */
export const globalPerformanceMonitor = new UnifiedPerformanceMonitor();

/**
 * Decorator for automatically timing method calls
 */
export function timed(operationName?: string) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    const opName = operationName || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      const timer = globalPerformanceMonitor.startTimer(opName);

      try {
        const result = await originalMethod.apply(this, args);
        timer.stop({ success: true });
        return result;
      } catch (error) {
        timer.stop({
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Utility function to measure async operations
 */
export async function measureAsync<T>(
  operationName: string,
  operation: () => Promise<T>,
  metadata?: Record<string, unknown>
): Promise<{ result: T; duration: number }> {
  const timer = globalPerformanceMonitor.startTimer(operationName);
  timer.metadata = metadata;

  try {
    const result = await operation();
    const duration = timer.stop({ success: true });
    return { result, duration };
  } catch (error) {
    const duration = timer.stop({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

/**
 * Utility function to measure sync operations
 */
export function measureSync<T>(
  operationName: string,
  operation: () => T,
  metadata?: Record<string, unknown>
): { result: T; duration: number } {
  const timer = globalPerformanceMonitor.startTimer(operationName);
  timer.metadata = metadata;

  try {
    const result = operation();
    const duration = timer.stop({ success: true });
    return { result, duration };
  } catch (error) {
    const duration = timer.stop({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

/**
 * Performance-optimized file system operations
 */
export class OptimizedFileOperations {
  private static fileStatsCache = CacheManager.getCache("file_stats", {
    defaultTtl: 30000, // 30 seconds
    maxSize: 1000,
  });

  private static fileContentCache = CacheManager.getCache("file_content", {
    defaultTtl: 60000, // 1 minute
    maxSize: 100,
  });

  /**
   * Cached file stats check
   */
  static async getFileStats(filePath: string) {
    return this.fileStatsCache.getOrSet(filePath, async () => {
      const fs = await import("fs/promises");
      return fs.stat(filePath);
    });
  }

  /**
   * Cached file existence check
   */
  static async fileExists(filePath: string): Promise<boolean> {
    try {
      await this.getFileStats(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Cached file content reading (for small files)
   */
  static async readFileContent(
    filePath: string,
    maxSize = 1024 * 1024
  ): Promise<string> {
    const stats = (await this.getFileStats(filePath)) as any;

    if (stats.size > maxSize) {
      // Don't cache large files
      const fs = await import("fs/promises");
      return fs.readFile(filePath, "utf-8");
    }

    return (await this.fileContentCache.getOrSet(
      `${filePath}_${stats.mtime.getTime()}`,
      async () => {
        const fs = await import("fs/promises");
        return fs.readFile(filePath, "utf-8");
      }
    )) as string;
  }
}

// Backward compatibility exports
export const PerformanceMonitor = UnifiedPerformanceMonitor;
export const PerformanceRegressionDetector = UnifiedPerformanceMonitor;
export const globalRegressionDetector = globalPerformanceMonitor;
