import {
  McpServer,
  ResourceTemplate,
} from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import * as fs from "fs/promises";
import * as fsSync from "fs";
import * as path from "path";
import * as os from "os";
import * as dotenv from "dotenv";

import { ServerConfig, ActiveProject } from "./types/index.js";
import {
  XcodeServerError,
  ProjectNotFoundError,
  ConfigurationError,
} from "./utils/errors.js";
import {
  findXcodeProjects,
  findProjectByName,
  getProjectInfo,
} from "./utils/projectManager.js";

// Import dependency injection and service management
import {
  ServiceContainer,
  initializeGlobalContainer,
  disposeGlobalContainer,
} from "./services/service-container.js";
import { PathManager } from "./utils/pathManager.js";
import { ProjectDirectoryState } from "./utils/projectDirectoryState.js";
import { SecureCommandExecutor } from "./services/command-service.js";
import { CacheManager, CacheService } from "./services/cache-service.js";
import { StringUtils } from "./utils/stringUtilities.js";
import { globalPerformanceMonitor } from "./utils/performanceMonitor.js";

// Load environment variables from .env file
dotenv.config();

// Consolidated tool registration functions
import { registerProjectTools } from "./tools/project-tools.js";
import { registerFileTools } from "./tools/file-tools.js";
import { registerBuildTools } from "./tools/build-tools.js";
import { registerPackageTools } from "./tools/package-tools.js";
import { registerSimulatorTools } from "./tools/simulator-tools.js";
import { registerXcodeTools } from "./tools/xcode-tools.js";
import { registerDevelopmentTools } from "./tools/development-tools.js";

export class XcodeServer {
  public server: McpServer;
  public config: ServerConfig = {};
  public activeProject: ActiveProject | null = null;
  public projectFiles: Map<string, string[]> = new Map();

  // Service container for dependency injection
  private serviceContainer: ServiceContainer;

  // Service instances (injected via container)
  public pathManager: PathManager;
  public directoryState: ProjectDirectoryState;
  public commandExecutor: typeof SecureCommandExecutor;
  public cache: CacheService;

  constructor(config: ServerConfig = {}) {
    // Start with default config
    this.config = { ...this.config, ...config };

    // Use environment variable for projects base directory if not explicitly provided
    if (!this.config.projectsBaseDir && process.env.PROJECTS_BASE_DIR) {
      this.config.projectsBaseDir = process.env.PROJECTS_BASE_DIR;
      console.error(
        `Using projects base directory from env: ${this.config.projectsBaseDir}`
      );
    }

    // If still no projects base directory, try some sensible defaults
    if (!this.config.projectsBaseDir) {
      this.config.projectsBaseDir = this.findDefaultProjectsDirectory();
    }

    // Initialize dependency injection container
    this.serviceContainer = initializeGlobalContainer(this.config);

    // Resolve services from container
    this.pathManager =
      this.serviceContainer.resolve<PathManager>("pathManager");
    this.directoryState =
      this.serviceContainer.resolve<ProjectDirectoryState>("directoryState");
    this.commandExecutor =
      this.serviceContainer.resolve<typeof SecureCommandExecutor>(
        "commandExecutor"
      );
    this.cache = this.serviceContainer.resolve<CacheService>("projectCache");

    // Create the MCP server
    this.server = new McpServer(
      {
        name: "xcode-server",
        version: "1.0.3",
        description: "An MCP server for Xcode integration",
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Enable debug logging if DEBUG is set
    if (process.env.DEBUG === "true") {
      console.error("Debug mode enabled");
    }

    // Initialize enhanced features
    this.initializeEnhancedFeatures();

    // Register all tools
    this.registerAllTools();
    this.registerResources();

    // Attempt to auto-detect an active project with more robust handling
    this.detectActiveProject()
      .then((project) => {
        if (project) {
          console.error(
            `Successfully detected active project: ${project.name} (${project.path})`
          );
        } else {
          console.error(
            "No active project detected automatically. Use set_project_path to set one."
          );
        }
      })
      .catch((error) => {
        console.error("Error detecting active project:", error.message);
      });
  }

  /**
   * Find a default projects directory from common locations
   */
  private findDefaultProjectsDirectory(): string | undefined {
    // Common locations for Xcode projects
    const possibleDirs = [
      path.join(os.homedir(), "Documents"),
      path.join(os.homedir(), "Projects"),
      path.join(os.homedir(), "Developer"),
      path.join(os.homedir(), "Documents/XcodeProjects"),
      path.join(os.homedir(), "Documents/Projects"),
    ];

    // Use the first directory that exists
    for (const dir of possibleDirs) {
      try {
        if (fsSync.existsSync(dir)) {
          console.error(
            `No projects base directory specified, using default: ${dir}`
          );
          return dir;
        }
      } catch (error) {
        // Ignore errors and try the next directory
      }
    }

    return undefined;
  }

  /**
   * Initialize enhanced features
   */
  private initializeEnhancedFeatures(): void {
    try {
      // Initialize advanced caching
      CacheManager.getCache("project-cache", {
        maxSize: 1000,
        defaultTtl: 300000, // 5 minutes
      });

      // Initialize performance regression detection
      globalPerformanceMonitor.addAlertCallback((regression) => {
        console.warn(
          `Performance regression detected in ${
            regression.operationName
          }: ${regression.regressionPercentage.toFixed(1)}% slower`
        );
      });

      console.error("Enhanced features initialized successfully");
    } catch (error) {
      console.error("Error initializing enhanced features:", error);
    }
  }

  /**
   * Cleanup resources when the server is disposed
   */
  public dispose(): void {
    try {
      if (this.serviceContainer) {
        this.serviceContainer.dispose();
      }
      disposeGlobalContainer();
      CacheManager.disposeAll();
    } catch (error) {
      console.error("Error disposing server resources:", error);
    }
  }

  private registerAllTools() {
    // Register all consolidated tools
    registerProjectTools(this); // 12 project management tools
    registerFileTools(this); // 13 file operation tools
    registerBuildTools(this); // 7 build system tools
    registerPackageTools(this); // 15 package management tools (CocoaPods + SPM)
    registerSimulatorTools(this); // 11 simulator control tools
    registerXcodeTools(this); // 8 Xcode utility tools
    registerDevelopmentTools(this); // 4 development tools

    console.error("✅ All consolidated tools registered successfully");
  }

  private registerResources() {
    // Resource to list available Xcode projects.
    this.server.resource(
      "xcode-projects",
      new ResourceTemplate("xcode://projects", { list: undefined }),
      async () => {
        const projects = await findXcodeProjects(
          this.config.projectsBaseDir,
          this.commandExecutor
        );
        return {
          contents: projects.map((project) => ({
            uri: `xcode://projects/${encodeURIComponent(project.name)}`,
            text: project.name,
            mimeType: "application/x-xcode-project" as const,
          })),
        };
      }
    );

    // Resource to get project details
    this.server.resource(
      "xcode-project",
      new ResourceTemplate("xcode://projects/{name}", { list: undefined }),
      async (uri, { name }) => {
        const decodedName = decodeURIComponent(name as string);
        const project = await findProjectByName(
          decodedName,
          this.config.projectsBaseDir,
          this.commandExecutor
        );
        if (!project) {
          throw new Error(`Project ${decodedName} not found`);
        }
        return {
          contents: [
            {
              uri: uri.href,
              text: JSON.stringify(project, null, 2),
              mimeType: "application/json" as const,
            },
          ],
        };
      }
    );
  }

  /**
   * Enhanced dynamic project detection with intelligent switching
   * @returns The detected active project or null if none found
   */
  public async detectActiveProject(): Promise<ActiveProject | null> {
    try {
      // Method 1: Get the frontmost Xcode project via AppleScript
      const frontmostProject = await this.detectFrontmostXcodeProject();
      if (frontmostProject) {
        console.error(
          `✅ Detected frontmost Xcode project: ${frontmostProject.name}`
        );
        return frontmostProject;
      }

      // Method 2: Get all open Xcode projects and select the most recent
      const openProjects = await this.getOpenXcodeProjects();
      if (openProjects.length > 0) {
        const mostRecent = openProjects[0]; // Already sorted by recency
        console.error(
          `✅ Selected most recent open project: ${mostRecent.name}`
        );
        return this.setActiveProject(mostRecent.path);
      }

      // Method 3: Scan base directory for recently modified projects
      if (this.config.projectsBaseDir) {
        const recentProject = await this.findMostRecentProject();
        if (recentProject) {
          console.error(
            `✅ Found most recent project in base directory: ${recentProject.name}`
          );
          return recentProject;
        }
      }

      // Method 4: Check Xcode recent projects from defaults
      const recentFromDefaults = await this.getRecentProjectFromDefaults();
      if (recentFromDefaults) {
        console.error(
          `✅ Found recent project from Xcode defaults: ${recentFromDefaults.name}`
        );
        return recentFromDefaults;
      }

      console.error(
        "⚠️  No active project detected - use set_project_path to set one"
      );
      return null;
    } catch (error) {
      console.error(
        "Error in project detection:",
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  }

  /**
   * Detect the frontmost Xcode project
   */
  private async detectFrontmostXcodeProject(): Promise<ActiveProject | null> {
    try {
      const result = await this.commandExecutor.execute(
        "osascript",
        [
          "-e",
          `tell application "Xcode"
            if it is running then
              set projectFile to path of document 1
              return POSIX path of projectFile
            end if
          end tell`,
        ],
        { timeout: 10000 }
      );

      const frontmostProject = result.stdout?.trim();
      if (!frontmostProject) return null;

      return this.setActiveProject(frontmostProject);
    } catch (error) {
      if (process.env.DEBUG === "true") {
        console.error(
          "Could not detect frontmost Xcode project:",
          error instanceof Error ? error.message : String(error)
        );
      }
      return null;
    }
  }

  /**
   * Get all currently open Xcode projects
   */
  private async getOpenXcodeProjects(): Promise<
    Array<{ path: string; name: string; lastModified: Date }>
  > {
    try {
      const result = await this.commandExecutor.execute(
        "osascript",
        [
          "-e",
          `tell application "Xcode"
            if it is running then
              set projectPaths to {}
              repeat with doc in documents
                try
                  set end of projectPaths to POSIX path of (path of doc)
                end try
              end repeat
              return projectPaths as string
            end if
          end tell`,
        ],
        { timeout: 15000 }
      );

      const pathsString = result.stdout?.trim();
      if (!pathsString) return [];

      const paths = pathsString
        .split(",")
        .map((p) => p.trim())
        .filter(Boolean);
      const projects = [];

      for (const projectPath of paths) {
        try {
          const stats = await fs.stat(projectPath);
          projects.push({
            path: projectPath,
            name: path.basename(projectPath, path.extname(projectPath)),
            lastModified: stats.mtime,
          });
        } catch (error) {
          // Skip projects that can't be accessed
        }
      }

      // Sort by last modified (most recent first)
      return projects.sort(
        (a, b) => b.lastModified.getTime() - a.lastModified.getTime()
      );
    } catch (error) {
      if (process.env.DEBUG === "true") {
        console.error(
          "Could not get open Xcode projects:",
          error instanceof Error ? error.message : String(error)
        );
      }
      return [];
    }
  }

  /**
   * Set active project with enhanced validation and setup
   */
  public async setActiveProject(
    projectPath: string
  ): Promise<ActiveProject | null>;
  public async setActiveProject(
    project: ActiveProject
  ): Promise<ActiveProject | null>;
  public async setActiveProject(
    projectPathOrObject: string | ActiveProject
  ): Promise<ActiveProject | null> {
    try {
      let projectPath: string;

      // Handle both string path and ActiveProject object
      if (typeof projectPathOrObject === "string") {
        projectPath = projectPathOrObject;
      } else {
        // If it's an ActiveProject object, just set it directly
        this.activeProject = projectPathOrObject;
        this.pathManager.setActiveProject(projectPathOrObject.path);
        const projectRoot = path.dirname(projectPathOrObject.path);
        this.directoryState.setActiveDirectory(projectRoot);
        return this.activeProject;
      }

      // Validate path boundaries
      if (
        this.config.projectsBaseDir &&
        !this.pathManager.isPathWithin(this.config.projectsBaseDir, projectPath)
      ) {
        console.error("⚠️  Project is outside the configured base directory");
      }

      // Clean up path if needed
      let cleanedPath = projectPath;
      if (projectPath.endsWith("/project.xcworkspace")) {
        cleanedPath = projectPath.replace("/project.xcworkspace", "");
      }

      const isWorkspace = cleanedPath.endsWith(".xcworkspace");
      let associatedProjectPath;

      // Find associated project for workspaces
      if (isWorkspace) {
        try {
          const { findMainProjectInWorkspace } = await import(
            "./utils/projectManager.js"
          );
          associatedProjectPath = await findMainProjectInWorkspace(
            cleanedPath,
            true
          );
        } catch (error) {
          if (process.env.DEBUG === "true") {
            console.error(
              `Error finding main project in workspace ${cleanedPath}:`,
              error instanceof Error ? error.message : String(error)
            );
          }
        }
      }

      // Create active project object
      this.activeProject = {
        path: cleanedPath,
        name: path.basename(cleanedPath, path.extname(cleanedPath)),
        isWorkspace,
        associatedProjectPath,
      };

      // Update path manager and directory state
      this.pathManager.setActiveProject(cleanedPath);
      const projectRoot = path.dirname(cleanedPath);
      this.directoryState.setActiveDirectory(projectRoot);

      return this.activeProject;
    } catch (error) {
      console.error(
        "Error setting active project:",
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  }

  /**
   * Find the most recently modified project in the base directory
   */
  private async findMostRecentProject(): Promise<ActiveProject | null> {
    if (!this.config.projectsBaseDir) return null;

    try {
      const projects = await findXcodeProjects(
        this.config.projectsBaseDir,
        this.commandExecutor
      );
      if (projects.length === 0) return null;

      const projectStats = await Promise.all(
        projects.map(async (project) => ({
          project,
          stats: await fs.stat(project.path),
        }))
      );

      const mostRecent = projectStats.sort(
        (a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime()
      )[0];

      return this.setActiveProject(mostRecent.project.path);
    } catch (error) {
      if (process.env.DEBUG === "true") {
        console.error(
          "Error scanning projects directory:",
          error instanceof Error ? error.message : String(error)
        );
      }
      return null;
    }
  }

  /**
   * Get recent project from Xcode defaults
   */
  private async getRecentProjectFromDefaults(): Promise<ActiveProject | null> {
    try {
      const result = await this.commandExecutor.execute(
        "defaults",
        ["read", "com.apple.dt.Xcode", "IDERecentWorkspaceDocuments"],
        { timeout: 5000 }
      );

      const recentProjects = result.stdout;
      if (!recentProjects) return null;

      const projectMatch = recentProjects.match(/= \\"([^"]+)"/);
      if (!projectMatch) return null;

      const recentProject = projectMatch[1];
      return this.setActiveProject(recentProject);
    } catch (error) {
      if (process.env.DEBUG === "true") {
        console.error(
          "Error reading Xcode defaults:",
          error instanceof Error ? error.message : String(error)
        );
      }
      return null;
    }
  }

  /**
   * Get all available Xcode projects with enhanced information
   */
  public async getAllAvailableProjects(): Promise<
    Array<{
      path: string;
      name: string;
      type: "project" | "workspace";
      lastModified: Date;
      isOpen: boolean;
      size: number;
    }>
  > {
    const allProjects: Array<{
      path: string;
      name: string;
      type: "project" | "workspace";
      lastModified: Date;
      isOpen: boolean;
      size: number;
    }> = [];

    try {
      // Get open projects
      const openProjects = await this.getOpenXcodeProjects();
      const openProjectPaths = new Set(openProjects.map((p) => p.path));

      // Scan base directory if configured
      if (this.config.projectsBaseDir) {
        const foundProjects = await findXcodeProjects(
          this.config.projectsBaseDir,
          this.commandExecutor
        );

        for (const project of foundProjects) {
          try {
            const stats = await fs.stat(project.path);
            allProjects.push({
              path: project.path,
              name: project.name,
              type: project.isWorkspace ? "workspace" : "project",
              lastModified: stats.mtime,
              isOpen: openProjectPaths.has(project.path),
              size: stats.size,
            });
          } catch (error) {
            // Skip projects that can't be accessed
          }
        }
      }

      // Add any open projects not found in base directory
      for (const openProject of openProjects) {
        if (!allProjects.some((p) => p.path === openProject.path)) {
          try {
            const stats = await fs.stat(openProject.path);
            allProjects.push({
              path: openProject.path,
              name: openProject.name,
              type: openProject.path.endsWith(".xcworkspace")
                ? "workspace"
                : "project",
              lastModified: openProject.lastModified,
              isOpen: true,
              size: stats.size,
            });
          } catch (error) {
            // Skip projects that can't be accessed
          }
        }
      }

      // Sort by last modified (most recent first)
      return allProjects.sort(
        (a, b) => b.lastModified.getTime() - a.lastModified.getTime()
      );
    } catch (error) {
      console.error(
        "Error getting all available projects:",
        error instanceof Error ? error.message : String(error)
      );
      return [];
    }
  }

  /**
   * Switch to a different project by name or path
   */
  public async switchToProject(
    identifier: string
  ): Promise<ActiveProject | null> {
    try {
      // First, try to find by exact path
      if (identifier.includes("/")) {
        const expandedPath = this.pathManager.expandPath(identifier);
        const fs = require("fs/promises");
        try {
          await fs.access(expandedPath);
          return this.setActiveProject(expandedPath);
        } catch {
          // File doesn't exist, continue with other methods
        }
      }

      // Try to find by name in available projects
      const allProjects = await this.getAllAvailableProjects();
      const matchingProject = allProjects.find(
        (p) =>
          p.name.toLowerCase() === identifier.toLowerCase() ||
          p.path.toLowerCase().includes(identifier.toLowerCase())
      );

      if (matchingProject) {
        return this.setActiveProject(matchingProject.path);
      }

      throw new Error(`Project not found: ${identifier}`);
    } catch (error) {
      console.error(
        "Error switching project:",
        error instanceof Error ? error.message : String(error)
      );
      return null;
    }
  }

  /**
   * Start the server with MCP protocol
   */
  public async start() {
    try {
      // Initialize transport
      const transport = new StdioServerTransport();

      // Connect to MCP protocol
      await this.server.connect(transport);
    } catch (error) {
      if (error instanceof Error) {
        throw new XcodeServerError(
          `Server initialization failed: ${error.message}`
        );
      }
      throw new XcodeServerError(
        `Server initialization failed: ${String(error)}`
      );
    }
  }
}
