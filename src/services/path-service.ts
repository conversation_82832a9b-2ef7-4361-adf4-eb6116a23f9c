/**
 * Consolidated Path Service
 * Merges functionality from pathManager.ts and pathUtilities.ts
 */

import * as path from "path";
import * as os from "os";
import { ServerConfig } from "../types/index.js";
import { PathAccessError } from "../utils/errors.js";

/**
 * Path validation result
 */
export interface PathValidationResult {
  isValid: boolean;
  normalizedPath: string;
  error?: string;
}

/**
 * Path expansion options
 */
export interface PathExpansionOptions {
  expandHome?: boolean;
  expandEnv?: boolean;
  resolveSymlinks?: boolean;
}

/**
 * Comprehensive Path Service for secure and consistent path handling
 */
export class PathService {
  private projectsBaseDir: string | undefined;
  private activeProjectPath: string | undefined;
  private activeProjectRoot: string | undefined;
  private serverRoot: string;
  private directoryHistory: string[] = [];

  constructor(config: ServerConfig = {}) {
    this.projectsBaseDir = config.projectsBaseDir
      ? this.expandPath(config.projectsBaseDir)
      : undefined;
    this.serverRoot = process.cwd();
  }

  /**
   * Expand path with home directory and environment variables
   */
  expandPath(inputPath: string, options: PathExpansionOptions = {}): string {
    const {
      expandHome = true,
      expandEnv = true,
      resolveSymlinks = false,
    } = options;

    let expandedPath = inputPath;

    // Expand home directory
    if (expandHome && expandedPath.startsWith("~")) {
      expandedPath = expandedPath.replace(/^~/, os.homedir());
    }

    // Expand environment variables
    if (expandEnv) {
      expandedPath = expandedPath.replace(/\$([A-Z_][A-Z0-9_]*)/g, (match, varName) => {
        return process.env[varName] || match;
      });
    }

    // Normalize the path
    expandedPath = this.normalizePath(expandedPath);

    // Resolve symlinks if requested
    if (resolveSymlinks) {
      try {
        const fs = require("fs");
        expandedPath = fs.realpathSync(expandedPath);
      } catch {
        // If symlink resolution fails, continue with the normalized path
      }
    }

    return expandedPath;
  }

  /**
   * Normalize path for consistent handling across platforms
   */
  normalizePath(inputPath: string): string {
    if (!inputPath) return "";

    // Convert to forward slashes and normalize
    let normalized = path.normalize(inputPath.replace(/\\/g, "/"));

    // Remove trailing slash except for root
    if (normalized.length > 1 && normalized.endsWith("/")) {
      normalized = normalized.slice(0, -1);
    }

    return normalized;
  }

  /**
   * Resolve a path relative to the active project or server root
   */
  resolvePath(relativePath: string): string {
    const normalizedPath = this.normalizePath(relativePath);

    // If it's already an absolute path, just normalize it
    if (path.isAbsolute(normalizedPath)) {
      return normalizedPath;
    }

    // If we have an active project, resolve relative to project root
    if (this.activeProjectRoot) {
      return path.join(this.activeProjectRoot, normalizedPath);
    }

    // Fallback to resolving relative to server root
    return path.join(this.serverRoot, normalizedPath);
  }

  /**
   * Resolve a path relative to the active project
   */
  resolveProjectPath(relativePath: string): string {
    const normalizedPath = this.normalizePath(relativePath);

    // If it's already an absolute path, just normalize it
    if (path.isAbsolute(normalizedPath)) {
      return normalizedPath;
    }

    // If we have an active project, resolve relative to project root
    if (this.activeProjectRoot) {
      return path.join(this.activeProjectRoot, normalizedPath);
    }

    // Fallback to resolving relative to server root
    return path.join(this.serverRoot, normalizedPath);
  }

  /**
   * Validate path for reading operations
   */
  validatePathForReading(targetPath: string): string {
    const normalizedPath = this.expandPath(targetPath);
    
    if (!this.isPathAllowed(normalizedPath)) {
      throw new PathAccessError(
        `Access denied: Path '${targetPath}' is outside allowed directories`
      );
    }

    return normalizedPath;
  }

  /**
   * Validate path for writing operations
   */
  validatePathForWriting(targetPath: string): string {
    const normalizedPath = this.expandPath(targetPath);
    
    if (!this.isPathAllowed(normalizedPath)) {
      throw new PathAccessError(
        `Write access denied: Path '${targetPath}' is outside allowed directories`
      );
    }

    return normalizedPath;
  }

  /**
   * Check if a path is within allowed directories
   */
  isPathAllowed(targetPath: string): boolean {
    const normalizedPath = this.normalizePath(targetPath);

    // Always allow paths within the server root
    if (this.isPathWithin(this.serverRoot, normalizedPath)) {
      return true;
    }

    // Allow paths within projects base directory if set
    if (this.projectsBaseDir && this.isPathWithin(this.projectsBaseDir, normalizedPath)) {
      return true;
    }

    // Allow paths within active project if set
    if (this.activeProjectPath && this.isPathWithin(this.activeProjectPath, normalizedPath)) {
      return true;
    }

    return false;
  }

  /**
   * Check if path is within another path
   */
  isPathWithin(parentPath: string, childPath: string): boolean {
    const normalizedParent = this.normalizePath(parentPath);
    const normalizedChild = this.normalizePath(childPath);

    return (
      normalizedChild === normalizedParent ||
      normalizedChild.startsWith(normalizedParent + path.sep)
    );
  }

  /**
   * Get relative path between two absolute paths
   */
  getRelativePath(from: string, to: string): string {
    return path.relative(this.normalizePath(from), this.normalizePath(to));
  }

  /**
   * Join paths safely with normalization
   */
  joinPaths(...paths: string[]): string {
    return this.normalizePath(path.join(...paths));
  }

  /**
   * Get the directory name of a path
   */
  getDirectoryName(filePath: string): string {
    return path.dirname(this.normalizePath(filePath));
  }

  /**
   * Get the base name of a path
   */
  getBaseName(filePath: string, ext?: string): string {
    return path.basename(this.normalizePath(filePath), ext);
  }

  /**
   * Get the extension of a path
   */
  getExtension(filePath: string): string {
    return path.extname(this.normalizePath(filePath));
  }

  /**
   * Validate path format and accessibility
   */
  validatePath(inputPath: string): PathValidationResult {
    try {
      const normalizedPath = this.expandPath(inputPath);
      
      if (!normalizedPath) {
        return {
          isValid: false,
          normalizedPath: "",
          error: "Empty path provided",
        };
      }

      if (!this.isPathAllowed(normalizedPath)) {
        return {
          isValid: false,
          normalizedPath,
          error: "Path is outside allowed directories",
        };
      }

      return {
        isValid: true,
        normalizedPath,
      };
    } catch (error) {
      return {
        isValid: false,
        normalizedPath: inputPath,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  // Configuration methods
  setProjectsBaseDir(baseDir: string): void {
    this.projectsBaseDir = this.expandPath(baseDir);
  }

  setActiveProject(projectPath: string): void {
    this.activeProjectPath = this.expandPath(projectPath);
    this.activeProjectRoot = this.getDirectoryName(this.activeProjectPath);
  }

  getProjectsBaseDir(): string | undefined {
    return this.projectsBaseDir;
  }

  getActiveProjectPath(): string | undefined {
    return this.activeProjectPath;
  }

  getActiveProjectRoot(): string | undefined {
    return this.activeProjectRoot;
  }

  getServerRoot(): string {
    return this.serverRoot;
  }

  // Directory history management
  pushDirectory(directory: string): void {
    this.directoryHistory.push(directory);
  }

  popDirectory(): string | undefined {
    return this.directoryHistory.pop();
  }

  getDirectoryHistory(): string[] {
    return [...this.directoryHistory];
  }

  clearDirectoryHistory(): void {
    this.directoryHistory = [];
  }
}

/**
 * Utility functions for backward compatibility
 */
export class PathUtils {
  /**
   * Expand path with home directory and environment variables
   */
  static expandPath(inputPath: string): string {
    const pathService = new PathService();
    return pathService.expandPath(inputPath);
  }

  /**
   * Normalize path for consistent handling
   */
  static normalizePath(inputPath: string): string {
    const pathService = new PathService();
    return pathService.normalizePath(inputPath);
  }

  /**
   * Join paths safely
   */
  static joinPaths(...paths: string[]): string {
    const pathService = new PathService();
    return pathService.joinPaths(...paths);
  }

  /**
   * Check if path is within another path
   */
  static isPathWithin(parentPath: string, childPath: string): boolean {
    const pathService = new PathService();
    return pathService.isPathWithin(parentPath, childPath);
  }
}
