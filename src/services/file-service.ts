/**
 * Consolidated File Service
 * Merges functionality from fileSystemManager.ts and related utilities
 */

import * as fs from "fs/promises";
import * as fsSync from "fs";
import * as path from "path";
import { PathService } from "./path-service.js";
import { FileOperationError } from "../utils/errors.js";

/**
 * File operation options
 */
export interface FileOptions {
  encoding?: BufferEncoding;
  createPath?: boolean;
  backup?: boolean;
  validatePath?: boolean;
  overwrite?: boolean;
  preserveTimestamps?: boolean;
}

/**
 * File information interface
 */
export interface FileInfo {
  path: string;
  name: string;
  extension: string;
  size: number;
  isDirectory: boolean;
  isFile: boolean;
  lastModified: Date;
  permissions: string;
  mimeType?: string;
}

/**
 * Directory listing options
 */
export interface DirectoryOptions {
  showHidden?: boolean;
  recursive?: boolean;
  maxDepth?: number;
  includeStats?: boolean;
  pattern?: string;
}

/**
 * Search options for file content
 */
export interface SearchOptions {
  caseSensitive?: boolean;
  isRegex?: boolean;
  maxResults?: number;
  includeHidden?: boolean;
  contextLines?: number;
}

/**
 * Search result interface
 */
export interface SearchResult {
  file: string;
  line: number;
  content: string;
  context?: {
    before: string[];
    after: string[];
  };
}

/**
 * Comprehensive File System Service
 */
export class FileService {
  private pathService: PathService;

  constructor(pathService?: PathService) {
    this.pathService = pathService || new PathService();
  }

  /**
   * Check if file or directory exists
   */
  async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file or directory stats
   */
  async getStats(filePath: string): Promise<fsSync.Stats> {
    try {
      return await fs.stat(filePath);
    } catch (error) {
      throw new FileOperationError(
        "stat",
        filePath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Get comprehensive file information
   */
  async getFileInfo(filePath: string): Promise<FileInfo> {
    const stats = await this.getStats(filePath);
    const parsedPath = path.parse(filePath);

    return {
      path: filePath,
      name: parsedPath.name,
      extension: parsedPath.ext,
      size: stats.size,
      isDirectory: stats.isDirectory(),
      isFile: stats.isFile(),
      lastModified: stats.mtime,
      permissions: stats.mode.toString(8),
      mimeType: this.getMimeType(parsedPath.ext),
    };
  }

  /**
   * Read file content
   */
  async readFile(
    filePath: string,
    options: {
      encoding?: BufferEncoding;
      validatePath?: boolean;
      asBinary?: boolean;
    } = {}
  ): Promise<string | Buffer> {
    const {
      encoding = "utf-8",
      validatePath = true,
      asBinary = false,
    } = options;

    let validatedPath = filePath;
    if (validatePath) {
      validatedPath = this.pathService.validatePathForReading(filePath);
    }

    try {
      if (asBinary) {
        return await fs.readFile(validatedPath);
      }
      return await fs.readFile(validatedPath, encoding);
    } catch (error) {
      throw new FileOperationError(
        "read",
        filePath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Write file content
   */
  async writeFile(
    filePath: string,
    content: string | Buffer,
    options: FileOptions = {}
  ): Promise<void> {
    const {
      encoding = "utf-8",
      createPath = true,
      backup = false,
      validatePath = true,
      overwrite = true,
    } = options;

    let validatedPath = filePath;
    if (validatePath) {
      validatedPath = this.pathService.validatePathForWriting(filePath);
    }

    // Create directory if needed
    if (createPath) {
      const dir = path.dirname(validatedPath);
      await fs.mkdir(dir, { recursive: true });
    }

    // Create backup if requested
    if (backup && (await this.exists(validatedPath))) {
      const backupPath = `${validatedPath}.backup.${Date.now()}`;
      await this.copyFile(validatedPath, backupPath);
    }

    // Check if file exists and overwrite is disabled
    if (!overwrite && (await this.exists(validatedPath))) {
      throw new FileOperationError(
        "write",
        filePath,
        new Error("File exists and overwrite is disabled")
      );
    }

    try {
      if (Buffer.isBuffer(content)) {
        await fs.writeFile(validatedPath, content);
      } else {
        await fs.writeFile(validatedPath, content, encoding);
      }
    } catch (error) {
      throw new FileOperationError(
        "write",
        filePath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Copy file or directory
   */
  async copyFile(
    source: string,
    destination: string,
    options: FileOptions = {}
  ): Promise<void> {
    const { overwrite = false, preserveTimestamps = true } = options;

    const sourceStats = await this.getStats(source);
    const isSourceDir = sourceStats.isDirectory();

    const destExists = await this.exists(destination);
    if (destExists && !overwrite) {
      throw new FileOperationError(
        "copy",
        destination,
        new Error("Destination exists and overwrite=false")
      );
    }

    try {
      if (isSourceDir) {
        await this.copyDirectory(source, destination, options);
      } else {
        await fs.copyFile(source, destination);

        if (preserveTimestamps) {
          const stats = await this.getStats(source);
          await fs.utimes(destination, stats.atime, stats.mtime);
        }
      }
    } catch (error) {
      throw new FileOperationError(
        "copy",
        source,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Move file or directory
   */
  async moveFile(source: string, destination: string): Promise<void> {
    try {
      await fs.rename(source, destination);
    } catch (error) {
      // If rename fails, try copy and delete
      try {
        await this.copyFile(source, destination, { overwrite: true });
        await this.deleteFile(source, { recursive: true });
      } catch (fallbackError) {
        throw new FileOperationError(
          "move",
          source,
          fallbackError instanceof Error
            ? fallbackError
            : new Error(String(fallbackError))
        );
      }
    }
  }

  /**
   * Delete file or directory
   */
  async deleteFile(
    filePath: string,
    options: { recursive?: boolean } = {}
  ): Promise<void> {
    const { recursive = false } = options;

    try {
      const stats = await this.getStats(filePath);

      if (stats.isDirectory()) {
        if (recursive) {
          await fs.rm(filePath, { recursive: true, force: true });
        } else {
          await fs.rmdir(filePath);
        }
      } else {
        await fs.unlink(filePath);
      }
    } catch (error) {
      throw new FileOperationError(
        "delete",
        filePath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Create directory
   */
  async createDirectory(dirPath: string, recursive = true): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive });
    } catch (error) {
      throw new FileOperationError(
        "mkdir",
        dirPath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * List directory contents
   */
  async listDirectory(
    dirPath: string,
    options: DirectoryOptions = {}
  ): Promise<string[] | FileInfo[]> {
    const {
      showHidden = false,
      recursive = false,
      maxDepth = 10,
      includeStats = false,
      pattern,
    } = options;

    try {
      const entries = await fs.readdir(dirPath);
      let filteredEntries = entries;

      // Filter hidden files
      if (!showHidden) {
        filteredEntries = entries.filter((entry) => !entry.startsWith("."));
      }

      // Apply pattern filter
      if (pattern) {
        const regex = new RegExp(pattern, "i");
        filteredEntries = filteredEntries.filter((entry) => regex.test(entry));
      }

      if (!includeStats && !recursive) {
        return filteredEntries;
      }

      const results: FileInfo[] = [];

      for (const entry of filteredEntries) {
        const fullPath = path.join(dirPath, entry);
        const info = await this.getFileInfo(fullPath);
        results.push(info);

        // Recursively list subdirectories
        if (recursive && info.isDirectory && maxDepth > 0) {
          const subResults = (await this.listDirectory(fullPath, {
            ...options,
            maxDepth: maxDepth - 1,
            includeStats: true,
          })) as FileInfo[];
          results.push(...subResults);
        }
      }

      return includeStats ? results : results.map((r) => r.path);
    } catch (error) {
      throw new FileOperationError(
        "readdir",
        dirPath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Search for text in files
   */
  async searchInFiles(
    directory: string,
    searchText: string,
    filePattern: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    const {
      caseSensitive = false,
      isRegex = false,
      maxResults = 100,
      includeHidden = false,
      contextLines = 2,
    } = options;

    const results: SearchResult[] = [];
    const files = await this.findFiles(directory, filePattern, {
      includeHidden,
    });

    const searchRegex = isRegex
      ? new RegExp(searchText, caseSensitive ? "g" : "gi")
      : new RegExp(
          searchText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
          caseSensitive ? "g" : "gi"
        );

    for (const file of files) {
      if (results.length >= maxResults) break;

      try {
        const content = (await this.readFile(file, {
          encoding: "utf-8",
        })) as string;
        const lines = content.split("\n");

        for (let i = 0; i < lines.length; i++) {
          if (results.length >= maxResults) break;

          if (searchRegex.test(lines[i])) {
            const result: SearchResult = {
              file,
              line: i + 1,
              content: lines[i],
            };

            // Add context if requested
            if (contextLines > 0) {
              result.context = {
                before: lines.slice(Math.max(0, i - contextLines), i),
                after: lines.slice(
                  i + 1,
                  Math.min(lines.length, i + 1 + contextLines)
                ),
              };
            }

            results.push(result);
          }
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }

    return results;
  }

  /**
   * Find files matching pattern
   */
  async findFiles(
    directory: string,
    pattern: string,
    options: { includeHidden?: boolean; maxDepth?: number } = {}
  ): Promise<string[]> {
    const { includeHidden = false, maxDepth = 10 } = options;
    const results: string[] = [];

    const searchDir = async (dir: string, depth: number) => {
      if (depth > maxDepth) return;

      try {
        const entries = await fs.readdir(dir);

        for (const entry of entries) {
          if (!includeHidden && entry.startsWith(".")) continue;

          const fullPath = path.join(dir, entry);
          const stats = await this.getStats(fullPath);

          if (stats.isFile()) {
            const regex = new RegExp(pattern.replace(/\*/g, ".*"), "i");
            if (regex.test(entry)) {
              results.push(fullPath);
            }
          } else if (stats.isDirectory()) {
            await searchDir(fullPath, depth + 1);
          }
        }
      } catch (error) {
        // Skip directories that can't be read
      }
    };

    await searchDir(directory, 0);
    return results;
  }

  /**
   * Get MIME type for file extension
   */
  private getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      ".txt": "text/plain",
      ".md": "text/markdown",
      ".json": "application/json",
      ".js": "application/javascript",
      ".ts": "application/typescript",
      ".swift": "text/x-swift",
      ".m": "text/x-objc",
      ".h": "text/x-c",
      ".xml": "application/xml",
      ".plist": "application/x-plist",
      ".png": "image/png",
      ".jpg": "image/jpeg",
      ".jpeg": "image/jpeg",
      ".gif": "image/gif",
      ".pdf": "application/pdf",
    };

    return mimeTypes[extension.toLowerCase()] || "application/octet-stream";
  }

  /**
   * Copy directory recursively
   */
  private async copyDirectory(
    source: string,
    destination: string,
    options: FileOptions = {}
  ): Promise<void> {
    await this.createDirectory(destination);

    const entries = await fs.readdir(source);

    for (const entry of entries) {
      const sourcePath = path.join(source, entry);
      const destPath = path.join(destination, entry);
      const stats = await this.getStats(sourcePath);

      if (stats.isDirectory()) {
        await this.copyDirectory(sourcePath, destPath, options);
      } else {
        await this.copyFile(sourcePath, destPath, options);
      }
    }
  }
}
